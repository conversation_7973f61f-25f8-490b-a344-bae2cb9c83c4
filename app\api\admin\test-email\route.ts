import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { getContactInfo } from '@/lib/mysql-database';

// POST - اختبار إرسال الإيميل
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({
        success: false,
        message: 'يرجى إدخال الإيميل'
      }, { status: 400 });
    }

    // جلب إعدادات الإيميل من قاعدة البيانات
    const contactInfo = await getContactInfo();

    if (!contactInfo?.email) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على إعدادات الإيميل في قاعدة البيانات'
      }, { status: 400 });
    }

    // جلب كلمة المرور من متغيرات البيئة
    const emailPass = process.env.EMAIL_PASS;
    const smtpHost = process.env.SMTP_HOST || 'smtp.hostinger.com';
    const smtpPort = parseInt(process.env.SMTP_PORT || '465');

    if (!emailPass) {
      return NextResponse.json({
        success: false,
        message: 'كلمة مرور الإيميل غير موجودة في متغيرات البيئة'
      }, { status: 400 });
    }

    console.log('🧪 بدء اختبار إرسال الإيميل...');
    console.log('📧 من:', contactInfo.email);
    console.log('📬 إلى:', email);
    console.log('🏠 خادم:', smtpHost);
    console.log('🔌 منفذ:', smtpPort);

    // إعداد transporter مع خيارات متعددة
    const transporterConfigs = [
      // الإعداد الأول: المنفذ 465 مع SSL
      {
        host: smtpHost,
        port: smtpPort,
        secure: smtpPort === 465,
        auth: {
          user: contactInfo.email,
          pass: emailPass
        },
        tls: {
          rejectUnauthorized: false
        }
      },
      // الإعداد البديل: المنفذ 587 مع TLS
      {
        host: smtpHost,
        port: 587,
        secure: false,
        auth: {
          user: contactInfo.email,
          pass: emailPass
        },
        tls: {
          rejectUnauthorized: false,
          starttls: {
            enable: true
          }
        }
      }
    ];

    let transporter = null;
    let usedConfig = null;

    // جرب الإعدادات المختلفة
    for (let i = 0; i < transporterConfigs.length; i++) {
      const config = transporterConfigs[i];
      console.log(`🔄 محاولة الإعداد ${i + 1}: منفذ ${config.port}`);
      
      try {
        const testTransporter = nodemailer.createTransport(config);
        await testTransporter.verify();
        transporter = testTransporter;
        usedConfig = config;
        console.log(`✅ نجح الاتصال مع الإعداد ${i + 1}`);
        break;
      } catch (error) {
        console.log(`❌ فشل الإعداد ${i + 1}:`, error instanceof Error ? error.message : 'Unknown error');
        if (i === transporterConfigs.length - 1) {
          throw error;
        }
      }
    }

    if (!transporter || !usedConfig) {
      throw new Error('فشل في الاتصال مع خادم SMTP');
    }

    // إنشاء محتوى الإيميل التجريبي
    const testEmailContent = `
      <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
            🧪 اختبار إعدادات البريد الإلكتروني - DROOB HAJER
          </h2>
          
          <p>مرحباً!</p>
          
          <p>هذا إيميل تجريبي للتأكد من أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
          
          <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #27ae60; margin: 0;">✅ الإعدادات المستخدمة:</h3>
            <ul style="margin: 10px 0;">
              <li><strong>الخادم:</strong> ${usedConfig.host}</li>
              <li><strong>المنفذ:</strong> ${usedConfig.port}</li>
              <li><strong>التشفير:</strong> ${usedConfig.secure ? 'SSL' : 'TLS'}</li>
              <li><strong>المرسل:</strong> ${contactInfo.email}</li>
              <li><strong>المستقبل:</strong> ${email}</li>
            </ul>
          </div>
          
          <p>إذا وصلك هذا الإيميل، فهذا يعني أن الإعدادات صحيحة ويمكن للنظام إرسال طلبات التسعير بنجاح.</p>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="color: #856404; margin: 0 0 10px 0;">📋 الخطوات التالية:</h4>
            <ol style="color: #856404; margin: 0;">
              <li>تأكد من وصول هذا الإيميل إلى صندوق الوارد</li>
              <li>تحقق من مجلد الرسائل المزعجة إذا لم تجده</li>
              <li>جرب إرسال طلب تسعير تجريبي من الموقع</li>
            </ol>
          </div>
          
          <p style="color: #7f8c8d; font-style: italic;">
            تم إرسال هذا الإيميل في: ${new Date().toLocaleString('ar-SA')}
          </p>
        </div>
      </div>
    `;

    // إرسال الإيميل التجريبي
    const mailOptions = {
      from: `"DROOB HAJER - اختبار" <${contactInfo.email}>`,
      to: email,
      subject: `🧪 اختبار إعدادات البريد الإلكتروني - ${new Date().toLocaleDateString('ar-SA')}`,
      html: testEmailContent
    };

    const info = await transporter.sendMail(mailOptions);
    
    console.log('✅ تم إرسال الإيميل التجريبي بنجاح!');
    console.log('🆔 Message ID:', info.messageId);
    console.log('📊 استجابة الخادم:', info.response);

    return NextResponse.json({
      success: true,
      message: 'تم إرسال الإيميل التجريبي بنجاح! تحقق من صندوق الوارد',
      details: {
        messageId: info.messageId,
        host: usedConfig.host,
        port: usedConfig.port,
        secure: usedConfig.secure
      }
    });

  } catch (error) {
    console.error('❌ خطأ في اختبار الإيميل:', error);
    
    let errorMessage = 'خطأ غير معروف في إرسال الإيميل';
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid login') || error.message.includes('authentication failed')) {
        errorMessage = 'خطأ في المصادقة: تحقق من الإيميل وكلمة المرور';
      } else if (error.message.includes('ECONNECTION') || error.message.includes('ETIMEDOUT')) {
        errorMessage = 'خطأ في الاتصال: تحقق من إعدادات الخادم والمنفذ';
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'خطأ في العثور على الخادم: تحقق من عنوان SMTP';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json({
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
