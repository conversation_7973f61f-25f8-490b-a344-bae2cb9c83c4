import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';
import {
  addQuoteRequest,
  addQuoteRequestProduct,
  getQuoteRequests
} from '@/lib/mysql-database';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}



// دالة إرسال الإيميل باستخدام Hostinger SMTP
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // جلب إعدادات الإيميل من قاعدة البيانات
    const { getContactInfo } = await import('@/lib/mysql-database');
    const contactInfo = await getContactInfo();

    if (!contactInfo?.email || !contactInfo?.whatsapp_number) {
      console.log('📧 إعدادات الإيميل غير مكتملة في قاعدة البيانات');
      console.log('💡 يرجى إضافة إعدادات الإيميل في لوحة التحكم');
      return { success: false, error: 'Email settings not configured in database' };
    }

    // استخدام إعدادات من متغيرات البيئة كبديل للكلمة المرور والخادم
    const emailPass = process.env.EMAIL_PASS;
    const smtpHost = process.env.SMTP_HOST || 'smtp.hostinger.com';
    const smtpPort = parseInt(process.env.SMTP_PORT || '465');

    if (!emailPass) {
      console.log('📧 كلمة مرور الإيميل غير موجودة في متغيرات البيئة');
      return { success: false, error: 'Email password not configured' };
    }

    console.log('📧 إعدادات الإيميل من قاعدة البيانات:');
    console.log('📧 الإيميل المرسل:', contactInfo.email);
    console.log('📧 الإيميل المستقبل:', contactInfo.email); // نفس الإيميل للإرسال والاستقبال
    console.log('🏠 الخادم:', smtpHost);
    console.log('🔌 المنفذ:', smtpPort);
    console.log('🔑 كلمة المرور متوفرة:', emailPass ? 'نعم' : 'لا');
    console.log('🔑 طول كلمة المرور:', emailPass ? emailPass.length : 0);

    // قراءة ملف Excel للإرسال
    const excelBuffer = fs.readFileSync(excelFilePath);

    // إرسال الإيميل باستخدام Hostinger SMTP
    return await sendWithHostinger(
      quoteRequest,
      {
        email: contactInfo.email,
        password: emailPass,
        host: smtpHost,
        port: smtpPort
      },
      contactInfo.email, // الإرسال لنفس الإيميل
      excelBuffer
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// تعريف نوع إعدادات الإيميل
interface EmailConfig {
  email: string;
  password: string;
  host: string;
  port: number;
}

// دالة إرسال الإيميل باستخدام Hostinger SMTP
async function sendWithHostinger(
  quoteRequest: QuoteRequest,
  emailConfig: EmailConfig,
  recipientEmail: string,
  excelBuffer: Buffer
) {
  try {
    console.log('🔄 محاولة إرسال إيميل عبر Hostinger SMTP...');
    console.log('📧 من:', emailConfig.email);
    console.log('📬 إلى:', recipientEmail);
    console.log('🏠 خادم:', emailConfig.host);
    console.log('🔌 منفذ:', emailConfig.port);

    // إنشاء transporter لـ Hostinger SMTP مع إعدادات محسنة
    const transporterConfig = {
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.port === 465, // true for 465, false for other ports
      auth: {
        user: emailConfig.email,
        pass: emailConfig.password
      },
      tls: {
        rejectUnauthorized: false, // للتعامل مع شهادات SSL المختلفة
        servername: emailConfig.host // إضافة servername للتحقق من الشهادة
      },
      connectionTimeout: 60000, // زيادة timeout
      greetingTimeout: 30000,
      socketTimeout: 60000,
      debug: false, // إيقاف التشخيص المفصل لتقليل الضوضاء
      logger: false
    };

    console.log('🔧 إعدادات SMTP المستخدمة:');
    console.log('🏠 Host:', transporterConfig.host);
    console.log('🔌 Port:', transporterConfig.port);
    console.log('🔒 Secure:', transporterConfig.secure);
    console.log('👤 User:', transporterConfig.auth.user);
    console.log('🔑 Pass length:', transporterConfig.auth.pass.length);

    // جرب إعدادات SMTP متعددة
    const smtpConfigs = [
      // الإعداد الأول: المنفذ 465 مع SSL
      {
        ...transporterConfig,
        port: 465,
        secure: true,
        name: 'SSL (465)'
      },
      // الإعداد الثاني: المنفذ 587 مع TLS
      {
        ...transporterConfig,
        port: 587,
        secure: false,
        tls: {
          rejectUnauthorized: false,
          starttls: { enable: true }
        },
        name: 'TLS (587)'
      }
    ];

    let transporter = null;
    let workingConfig = null;

    for (const config of smtpConfigs) {
      try {
        console.log(`🔄 جاري اختبار ${config.name}...`);
        const testTransporter = nodemailer.createTransport(config);
        await testTransporter.verify();

        transporter = testTransporter;
        workingConfig = config;
        console.log(`✅ نجح الاتصال مع ${config.name}`);
        break;
      } catch (error) {
        console.log(`❌ فشل ${config.name}:`, error instanceof Error ? error.message : 'Unknown error');
      }
    }

    if (!transporter || !workingConfig) {
      throw new Error('فشل في الاتصال مع جميع إعدادات SMTP المتاحة. تحقق من الإيميل وكلمة المرور في Hostinger.');
    }

    console.log(`✅ سيتم استخدام ${workingConfig.name} للإرسال`);

    // تحويل ملف Excel إلى base64
    const excelBase64 = excelBuffer.toString('base64');

    // محتوى الإيميل
    const emailContent = `
      <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
            طلب تسعير جديد - ${quoteRequest.customerInfo.company || quoteRequest.customerInfo.name}
          </h2>

          <div style="margin: 20px 0;">
            <h3 style="color: #34495e;">معلومات العميل:</h3>
            <p><strong>الاسم:</strong> ${quoteRequest.customerInfo.name}</p>
            <p><strong>الهاتف:</strong> ${quoteRequest.customerInfo.phone}</p>
            <p><strong>الإيميل:</strong> ${quoteRequest.customerInfo.email}</p>
            ${quoteRequest.customerInfo.company ? `<p><strong>الشركة:</strong> ${quoteRequest.customerInfo.company}</p>` : ''}
          </div>

          <div style="margin: 20px 0;">
            <h3 style="color: #34495e;">تفاصيل الطلب:</h3>
            <p><strong>رقم الطلب:</strong> ${quoteRequest.id}</p>
            <p><strong>عدد المنتجات:</strong> ${quoteRequest.products.length}</p>
            <p><strong>المجموع التقديري:</strong> ${quoteRequest.totalAmount.toLocaleString()} ريال</p>
            <p><strong>تاريخ الطلب:</strong> ${new Date(quoteRequest.createdAt).toLocaleDateString('ar-SA')}</p>
          </div>

          <div style="margin: 20px 0;">
            <h3 style="color: #34495e;">المنتجات المطلوبة:</h3>
            <ul style="list-style-type: none; padding: 0;">
              ${quoteRequest.products.map(product => `
                <li style="background-color: #ecf0f1; margin: 5px 0; padding: 10px; border-radius: 5px;">
                  <strong>${product.titleAr || product.title}</strong> - الكمية: ${product.quantity}
                </li>
              `).join('')}
            </ul>
          </div>

          <p style="color: #7f8c8d; font-style: italic;">
            ملف Excel مرفق مع التفاصيل الكاملة للمنتجات المطلوبة.
          </p>
        </div>
      </div>
    `;

    // إعدادات الإيميل
    const mailOptions = {
      from: `"DROOB HAJER" <${emailConfig.email}>`,
      to: recipientEmail,
      replyTo: quoteRequest.customerInfo.email,
      subject: `طلب تسعير جديد من ${quoteRequest.customerInfo.company || quoteRequest.customerInfo.name} - ${quoteRequest.id}`,
      html: emailContent,
      attachments: [
        {
          filename: `طلب-تسعير-${quoteRequest.id}.xlsx`,
          content: excelBase64,
          encoding: 'base64',
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      ]
    };

    // إرسال الإيميل
    const info = await transporter.sendMail(mailOptions);

    console.log('✅ تم إرسال الإيميل بنجاح عبر Hostinger SMTP!');
    console.log('🆔 Message ID:', info.messageId);
    console.log('📊 استجابة الخادم:', info.response);

    return {
      success: true,
      messageId: info.messageId,
      message: '🎉 تم إرسال طلب التسعير بنجاح! سنتواصل معك قريباً'
    };

  } catch (error) {
    console.error('❌ خطأ في إرسال الإيميل عبر Hostinger SMTP:');
    console.error('📧 الإيميل:', emailConfig.email);
    console.error('🏠 الخادم:', emailConfig.host);
    console.error('🔌 المنفذ:', emailConfig.port);
    console.error('📄 تفاصيل الخطأ:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'SMTP error'
    };
  }
}





// POST - إنشاء طلب تسعير جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { customerInfo, products } = body;

    // التحقق من صحة البيانات
    if (!customerInfo || !products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'بيانات غير صحيحة: يجب إرسال معلومات العميل والمنتجات'
      }, { status: 400 });
    }

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) =>
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد uploads/excel إذا لم يكن موجوداً
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      const excelDir = path.join(uploadsDir, 'excel');

      // التأكد من وجود المجلدات وإنشاؤها إذا لم تكن موجودة
      try {
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
          console.log('✅ تم إنشاء مجلد uploads:', uploadsDir);
        }

        if (!fs.existsSync(excelDir)) {
          fs.mkdirSync(excelDir, { recursive: true });
          console.log('✅ تم إنشاء مجلد excel:', excelDir);
        }

        // التحقق من صلاحيات الكتابة
        fs.accessSync(excelDir, fs.constants.W_OK);
        console.log('✅ تم التحقق من صلاحيات الكتابة في:', excelDir);
      } catch (error) {
        console.error('❌ خطأ في إنشاء المجلدات أو التحقق من الصلاحيات:', error);
        throw new Error(`Cannot create or access directory: ${excelDir}`);
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      let excelFilePath = path.join(excelDir, excelFileName);

      console.log('📁 مسار ملف Excel:', excelFilePath);
      console.log('📂 مجلد Excel:', excelDir);
      console.log('📄 اسم الملف:', excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');

      // كتابة ملف Excel مع معالجة الأخطاء
      try {
        console.log('📝 محاولة كتابة ملف Excel في:', excelFilePath);

        // الطريقة الأولى: استخدام XLSX.writeFile
        try {
          XLSX.writeFile(workbook, excelFilePath);
        } catch {
          console.log('⚠️  فشلت الطريقة الأولى، جاري المحاولة بطريقة بديلة...');

          // الطريقة البديلة: تحويل إلى buffer ثم كتابة الملف
          const excelBuffer = XLSX.write(workbook, {
            type: 'buffer',
            bookType: 'xlsx'
          });
          fs.writeFileSync(excelFilePath, excelBuffer);
        }

        console.log('✅ تم إنشاء ملف Excel بنجاح:', excelFilePath);

        // التحقق من وجود الملف
        if (!fs.existsSync(excelFilePath)) {
          throw new Error('File was not created successfully');
        }

        // التحقق من حجم الملف
        const stats = fs.statSync(excelFilePath);
        console.log('📊 حجم ملف Excel:', stats.size, 'bytes');

        if (stats.size === 0) {
          throw new Error('File was created but is empty');
        }
      } catch (excelError) {
        console.error('❌ خطأ في كتابة ملف Excel:', excelError);

        // محاولة أخيرة: إنشاء ملف CSV بدلاً من Excel
        try {
          console.log('🔄 محاولة إنشاء ملف CSV كبديل...');
          const csvFilePath = excelFilePath.replace('.xlsx', '.csv');
          const csvData = excelData.map(row =>
            Object.values(row).map(cell => `"${cell}"`).join(',')
          ).join('\n');

          const csvHeader = Object.keys(excelData[0]).map(key => `"${key}"`).join(',');
          const fullCsvData = csvHeader + '\n' + csvData;

          fs.writeFileSync(csvFilePath, fullCsvData, 'utf8');
          console.log('✅ تم إنشاء ملف CSV بديل:', csvFilePath);

          // تحديث مسار الملف للاستخدام في الإيميل
          excelFilePath = csvFilePath;
        } catch (csvError) {
          console.error('❌ فشل في إنشاء ملف CSV أيضاً:', csvError);
          throw new Error(`Cannot create Excel or CSV file: ${excelError instanceof Error ? excelError.message : 'Unknown error'}`);
        }
      }

      // حفظ طلب التسعير في قاعدة البيانات
      const quoteRequestData = {
        id: requestId,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        customer_company: customerInfo.company || null,
        excel_file_url: `uploads/excel/${excelFileName}`,
        status: 'pending' as const,
        notes: undefined
      };

      await addQuoteRequest(quoteRequestData);

      // حفظ المنتجات المرتبطة بطلب التسعير
      for (const product of products) {
        await addQuoteRequestProduct(requestId, product.id);
      }

      // إنشاء كائن طلب التسعير للإيميل (بالتنسيق القديم للتوافق)
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `data/excel/${excelFileName}`
      };

      // إرسال الإيميل مع ملف الإكسل
      const emailResult = await sendEmailWithExcel(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

    return NextResponse.json({
      success: true,
      message: emailResult.success
        ? '🎉 تم إرسال طلب التسعير بنجاح! سنتواصل معك قريباً'
        : '✅ تم حفظ طلب التسعير بنجاح، لكن لم يتم إرسال الإيميل',
      requestId,
      excelFilePath: quoteRequest.excelFilePath,
      emailSent: emailResult.success,
      emailError: emailResult.success ? null : emailResult.error
    });

  } catch (error) {
    console.error('Error creating quote request:', error);
    return NextResponse.json({
      success: false,
      message: 'عذراً، حدث خطأ تقني أثناء معالجة طلبك. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET - جلب جميع طلبات التسعير
export async function GET() {
    try {
      // جلب جميع طلبات التسعير من قاعدة البيانات
      const dbRequests = await getQuoteRequests();

      // تحويل البيانات إلى التنسيق المتوقع من الواجهة الأمامية
      const formattedRequests = dbRequests.map((request) => ({
        id: request.id,
        customerInfo: {
          name: request.customer_name,
          email: request.customer_email,
          phone: request.customer_phone,
          company: request.customer_company || ''
        },
        products: [], // سيتم ملؤها لاحقاً إذا لزم الأمر
        totalAmount: 0, // يمكن حسابها لاحقاً إذا لزم الأمر
        createdAt: request.created_at.toISOString(),
        status: request.status,
        excelFilePath: request.excel_file_url || ''
      }));

    return NextResponse.json({ success: true, requests: formattedRequests });

    } catch (error) {
      console.error('Error fetching quote requests:', error);
    return NextResponse.json({
      success: false,
      message: 'عذراً، حدث خطأ أثناء تحميل البيانات. يرجى إعادة تحميل الصفحة.',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
