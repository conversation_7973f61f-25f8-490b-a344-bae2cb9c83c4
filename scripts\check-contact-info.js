// سكريبت للتحقق من بيانات contact_info في قاعدة البيانات
const mysql = require('mysql2/promise');

async function checkContactInfo() {
  console.log('🔍 فحص بيانات contact_info...\n');

  // إعدادات قاعدة البيانات
  const dbConfig = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: 'droobhajer_db',
    charset: 'utf8mb4'
  };

  let connection;

  try {
    // الاتصال بقاعدة البيانات
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات\n');

    // فحص هيكل الجدول
    console.log('📋 هيكل جدول contact_info:');
    const [columns] = await connection.execute('DESCRIBE contact_info');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });
    console.log('');

    // جلب البيانات
    console.log('📊 البيانات الموجودة:');
    const [rows] = await connection.execute('SELECT * FROM contact_info');
    
    if (rows.length === 0) {
      console.log('📭 لا توجد بيانات في الجدول');
    } else {
      rows.forEach((row, index) => {
        console.log(`\n📄 السجل ${index + 1}:`);
        console.log(`  🆔 ID: ${row.id}`);
        console.log(`  📧 Email: ${row.email || 'غير محدد'}`);
        console.log(`  🔑 Password: ${row.Password ? '***مخفي***' : 'غير محدد'}`);
        console.log(`  🏠 Host: ${row.host || 'غير محدد'}`);
        console.log(`  🔌 Port: ${row.port || 'غير محدد'}`);
        console.log(`  📱 WhatsApp: ${row.whatsapp_number || 'غير محدد'}`);
        console.log(`  📅 Updated: ${row.updated_at || 'غير محدد'}`);
        
        // فحص إذا كانت البيانات مخزنة كـ JSON
        if (row.email && row.email.startsWith('{')) {
          console.log('  ⚠️  تحذير: البيانات مخزنة كـ JSON!');
          try {
            const parsed = JSON.parse(row.email);
            console.log('  📝 البيانات المحللة:', parsed);
          } catch (e) {
            console.log('  ❌ خطأ في تحليل JSON');
          }
        }
      });
    }

    // إحصائيات
    console.log(`\n📈 الإحصائيات:`);
    console.log(`  📊 عدد السجلات: ${rows.length}`);
    
    const validEmails = rows.filter(row => row.email && !row.email.startsWith('{')).length;
    const jsonEmails = rows.filter(row => row.email && row.email.startsWith('{')).length;
    
    console.log(`  ✅ إيميلات صحيحة: ${validEmails}`);
    console.log(`  ⚠️  إيميلات JSON: ${jsonEmails}`);

    // اقتراحات
    if (jsonEmails > 0) {
      console.log('\n💡 اقتراحات الإصلاح:');
      console.log('1. حذف السجلات التي تحتوي على JSON');
      console.log('2. إدراج بيانات جديدة بالتنسيق الصحيح');
      console.log('3. استخدام صفحة إعدادات الإيميل لإدخال البيانات');
    }

  } catch (error) {
    console.error('\n❌ خطأ في فحص البيانات:');
    console.error('📄 تفاصيل الخطأ:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل السكريبت
checkContactInfo().catch(console.error);
