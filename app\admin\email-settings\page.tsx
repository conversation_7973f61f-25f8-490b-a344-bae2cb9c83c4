'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface ContactInfo {
  id: number;
  email?: string;
  Password?: string;
  host?: string;
  port?: number;
  whatsapp_number?: string;
  updated_at: string;
}

export default function EmailSettingsPage() {
  const [contactInfo, setContactInfo] = useState<ContactInfo | null>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [host, setHost] = useState('smtp.hostinger.com');
  const [port, setPort] = useState('465');
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  // جلب الإعدادات الحالية
  useEffect(() => {
    fetchContactInfo();
  }, []);

  const fetchContactInfo = async () => {
    try {
      const response = await fetch('/api/admin/contact-info');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.contactInfo) {
          setContactInfo(data.contactInfo);
          setEmail(data.contactInfo.email || '');
          setPassword(data.contactInfo.Password || '');
          setHost(data.contactInfo.host || 'smtp.hostinger.com');
          setPort(data.contactInfo.port?.toString() || '465');
          setWhatsappNumber(data.contactInfo.whatsapp_number || '');
        }
      }
    } catch (error) {
      console.error('Error fetching contact info:', error);
      toast.error('خطأ في جلب الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/contact-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          Password: password.trim(),
          host: host.trim(),
          port: parseInt(port) || 465,
          whatsapp_number: whatsappNumber.trim()
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('تم حفظ الإعدادات بنجاح');
        setContactInfo(data.contactInfo);
      } else {
        toast.error(data.message || 'خطأ في حفظ الإعدادات');
      }
    } catch (error) {
      console.error('Error saving contact info:', error);
      toast.error('خطأ في حفظ الإعدادات');
    } finally {
      setSaving(false);
    }
  };

  const handleTestEmail = async () => {
    if (!email.trim()) {
      toast.error('يرجى إدخال الإيميل أولاً');
      return;
    }

    setTesting(true);
    try {
      const response = await fetch('/api/admin/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim()
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('تم إرسال إيميل تجريبي بنجاح! تحقق من صندوق الوارد');
      } else {
        toast.error(data.message || 'فشل في إرسال الإيميل التجريبي');
      }
    } catch (error) {
      console.error('Error testing email:', error);
      toast.error('خطأ في اختبار الإيميل');
    } finally {
      setTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">إعدادات البريد الإلكتروني</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="space-y-6">
            {/* إعدادات الإيميل */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                البريد الإلكتروني للشركة
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
                dir="ltr"
              />
              <p className="text-sm text-gray-500 mt-1">
                هذا الإيميل سيستخدم لإرسال واستقبال طلبات التسعير
              </p>
            </div>

            {/* كلمة مرور الإيميل */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                كلمة مرور الإيميل
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="كلمة مرور الإيميل من Hostinger"
                dir="ltr"
              />
              <p className="text-sm text-gray-500 mt-1">
                كلمة مرور حساب البريد الإلكتروني في Hostinger
              </p>
            </div>

            {/* خادم SMTP */}
            <div>
              <label htmlFor="host" className="block text-sm font-medium text-gray-700 mb-2">
                خادم SMTP
              </label>
              <input
                type="text"
                id="host"
                value={host}
                onChange={(e) => setHost(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="smtp.hostinger.com"
                dir="ltr"
              />
              <p className="text-sm text-gray-500 mt-1">
                عنوان خادم SMTP (عادة smtp.hostinger.com)
              </p>
            </div>

            {/* منفذ SMTP */}
            <div>
              <label htmlFor="port" className="block text-sm font-medium text-gray-700 mb-2">
                منفذ SMTP
              </label>
              <select
                id="port"
                value={port}
                onChange={(e) => setPort(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="465">465 (SSL)</option>
                <option value="587">587 (TLS)</option>
              </select>
              <p className="text-sm text-gray-500 mt-1">
                منفذ الاتصال (465 للـ SSL أو 587 للـ TLS)
              </p>
            </div>

            {/* رقم الواتساب */}
            <div>
              <label htmlFor="whatsapp" className="block text-sm font-medium text-gray-700 mb-2">
                رقم الواتساب
              </label>
              <input
                type="tel"
                id="whatsapp"
                value={whatsappNumber}
                onChange={(e) => setWhatsappNumber(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="+966501234567"
                dir="ltr"
              />
              <p className="text-sm text-gray-500 mt-1">
                رقم الواتساب للتواصل مع العملاء
              </p>
            </div>

            {/* معلومات إضافية */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="text-lg font-medium text-blue-800 mb-2">ملاحظات مهمة:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• يجب أن يكون الإيميل من نفس النطاق المستضاف على Hostinger</li>
                <li>• تأكد من إعداد كلمة مرور الإيميل في متغيرات البيئة (.env.local)</li>
                <li>• استخدم زر "اختبار الإيميل" للتأكد من عمل الإعدادات</li>
              </ul>
            </div>

            {/* الأزرار */}
            <div className="flex gap-4">
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </button>
              
              <button
                onClick={handleTestEmail}
                disabled={testing || !email.trim()}
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {testing ? 'جاري الاختبار...' : 'اختبار الإيميل'}
              </button>
            </div>

            {/* معلومات آخر تحديث */}
            {contactInfo && (
              <div className="text-sm text-gray-500 text-center">
                آخر تحديث: {new Date(contactInfo.updated_at).toLocaleString('ar-SA')}
              </div>
            )}
          </div>
        </div>

        {/* دليل الإعداد */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">دليل الإعداد السريع</h2>
          <div className="space-y-3 text-sm text-gray-600">
            <div>
              <strong>1. إعداد الإيميل في Hostinger:</strong>
              <p>اذهب إلى لوحة تحكم Hostinger → Email → إنشاء حساب بريد إلكتروني</p>
            </div>
            <div>
              <strong>2. إعداد متغيرات البيئة:</strong>
              <p>أضف كلمة مرور الإيميل في ملف .env.local</p>
            </div>
            <div>
              <strong>3. اختبار الإعدادات:</strong>
              <p>استخدم زر "اختبار الإيميل" للتأكد من عمل النظام</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
